"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
class DatabaseService {
    constructor(database) {
        this.db = database;
    }
    async initialize() {
        try {
            // Enable foreign keys
            this.db.pragma('foreign_keys = ON');
            // Read and execute schema
            const schemaPath = (0, path_1.join)(__dirname, '../../db/schema.sql');
            const schema = (0, fs_1.readFileSync)(schemaPath, 'utf-8');
            // Split schema by statements and execute each one
            const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);
            for (const statement of statements) {
                this.db.exec(statement + ';');
            }
            console.log('Database initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize database:', error);
            throw error;
        }
    }
    query(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            return stmt.all(params);
        }
        catch (error) {
            console.error('Database query error:', error);
            throw error;
        }
    }
    execute(sql, params = []) {
        try {
            const stmt = this.db.prepare(sql);
            return stmt.run(params);
        }
        catch (error) {
            console.error('Database execute error:', error);
            throw error;
        }
    }
    transaction(callback) {
        const transaction = this.db.transaction(callback);
        transaction();
    }
    close() {
        this.db.close();
    }
    // Helper methods for common operations
    getLastInsertId() {
        const result = this.query('SELECT last_insert_rowid() as id');
        return result[0]?.id || 0;
    }
    tableExists(tableName) {
        const result = this.query("SELECT name FROM sqlite_master WHERE type='table' AND name=?", [tableName]);
        return result.length > 0;
    }
    getTableInfo(tableName) {
        return this.query(`PRAGMA table_info(${tableName})`);
    }
    backup(backupPath) {
        this.db.backup(backupPath);
    }
}
exports.DatabaseService = DatabaseService;
