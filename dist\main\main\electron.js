"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const fs_1 = require("fs");
const better_sqlite3_1 = __importDefault(require("better-sqlite3"));
const DatabaseService_1 = require("./services/DatabaseService");
const FileService_1 = require("./services/FileService");
const GoogleDriveService_1 = require("./services/GoogleDriveService");
const common_1 = require("./types/common");
class ElectronApp {
    constructor() {
        this.mainWindow = null;
        this.databaseService = null;
        this.fileService = null;
        this.googleDriveService = null;
        this.initializeApp();
    }
    async initializeApp() {
        // Handle app events
        electron_1.app.whenReady().then(() => {
            this.createWindow();
            this.initializeServices();
            this.setupIpcHandlers();
        });
        electron_1.app.on("window-all-closed", () => {
            if (process.platform !== "darwin") {
                electron_1.app.quit();
            }
        });
        electron_1.app.on("activate", () => {
            if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                this.createWindow();
            }
        });
    }
    createWindow() {
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: (0, path_1.join)(__dirname, "preload.js"),
            },
            icon: (0, path_1.join)(__dirname, "../../public/icon.ico"),
            show: false, // Don't show until ready
            titleBarStyle: "default",
        });
        // Load the app
        if (process.env.NODE_ENV === "development") {
            this.mainWindow.loadURL("http://localhost:3000");
            this.mainWindow.webContents.openDevTools();
        }
        else {
            this.mainWindow.loadFile((0, path_1.join)(__dirname, "../renderer/index.html"));
        }
        // Show window when ready
        this.mainWindow.once("ready-to-show", () => {
            this.mainWindow?.show();
        });
        // Handle external links
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            electron_1.shell.openExternal(url);
            return { action: "deny" };
        });
    }
    async initializeServices() {
        try {
            // Ensure data directories exist
            const userDataPath = electron_1.app.getPath("userData");
            const dbPath = (0, path_1.join)(userDataPath, "database");
            const filesPath = (0, path_1.join)(userDataPath, "files");
            if (!(0, fs_1.existsSync)(dbPath))
                (0, fs_1.mkdirSync)(dbPath, { recursive: true });
            if (!(0, fs_1.existsSync)(filesPath))
                (0, fs_1.mkdirSync)(filesPath, { recursive: true });
            // Initialize database
            const dbFile = (0, path_1.join)(dbPath, "apartment_rental.db");
            const database = new better_sqlite3_1.default(dbFile);
            this.databaseService = new DatabaseService_1.DatabaseService(database);
            await this.databaseService.initialize();
            // Initialize file service
            this.fileService = new FileService_1.FileService(filesPath);
            // Initialize Google Drive service
            this.googleDriveService = new GoogleDriveService_1.GoogleDriveService();
            console.log("All services initialized successfully");
        }
        catch (error) {
            console.error("Failed to initialize services:", error);
            electron_1.dialog.showErrorBox("Initialization Error", "Failed to initialize application services. Please restart the application.");
        }
    }
    setupIpcHandlers() {
        // Database operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.DB_INIT, async () => {
            return this.databaseService?.initialize();
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.DB_QUERY, async (_, sql, params) => {
            return this.databaseService?.query(sql, params);
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.DB_EXECUTE, async (_, sql, params) => {
            return this.databaseService?.execute(sql, params);
        });
        // File operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FILE_UPLOAD, async (_, fileData, fileName, entityType, entityId, fieldName) => {
            return this.fileService?.uploadFile(fileData, fileName, entityType, entityId, fieldName);
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FILE_DELETE, async (_, filePath) => {
            return this.fileService?.deleteFile(filePath);
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.FILE_GENERATE_THUMBNAIL, async (_, filePath) => {
            return this.fileService?.generateThumbnail(filePath);
        });
        // Google Drive operations
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.GOOGLE_AUTH, async () => {
            return this.googleDriveService?.authenticate();
        });
        electron_1.ipcMain.handle(common_1.IPC_CHANNELS.GOOGLE_SYNC, async () => {
            return this.googleDriveService?.syncData();
        });
        // Dialog operations
        electron_1.ipcMain.handle("dialog:openFile", async (_, options) => {
            const result = await electron_1.dialog.showOpenDialog(this.mainWindow, options);
            return result;
        });
        electron_1.ipcMain.handle("dialog:saveFile", async (_, options) => {
            const result = await electron_1.dialog.showSaveDialog(this.mainWindow, options);
            return result;
        });
        electron_1.ipcMain.handle("dialog:showMessage", async (_, options) => {
            const result = await electron_1.dialog.showMessageBox(this.mainWindow, options);
            return result;
        });
    }
}
// Create the application instance
new ElectronApp();
