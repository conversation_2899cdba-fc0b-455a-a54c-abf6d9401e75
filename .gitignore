# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist/
build/
release/
out/

# Electron specific
*.app
*.dmg
*.exe
*.msi
*.pkg
*.deb
*.rpm
*.snap
*.AppImage

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Database files (SQLite)
*.db
*.sqlite
*.sqlite3
*.db-journal

# User data and app data
userData/
appData/

# File uploads and user content
uploads/
files/
thumbnails/

# Google Drive credentials and tokens
credentials.json
token.json
.google-credentials/

# Backup files
*.backup
*.bak

# Test files
test-results/
coverage/

# Webpack
.webpack/

# Electron-builder output
/dist_electron/

# Local configuration files
config.local.json
settings.local.json

# Certificate files
*.p12
*.pem
*.key
*.crt
*.cer

# Temporary files created by editors
*~
.#*
\#*#
.*.swp
.*.swo

# Package lock files (choose one based on your package manager)
# Uncomment the one you're NOT using:
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
