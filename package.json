{"name": "apartment-rental-management", "version": "1.0.0", "description": "Building Apartment Rental Management System", "main": "dist/main/electron.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite", "dev:main": "tsc -p src/main/tsconfig.json && electron dist/main/electron.js", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p src/main/tsconfig.json", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["electron", "react", "typescript", "rental-management", "apartment-management"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-react": "^4.1.1", "concurrently": "^8.2.2", "electron": "^27.1.2", "electron-builder": "^24.6.4", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "typescript": "^5.2.2", "vite": "^4.5.0"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.19", "@mui/x-date-pickers": "^6.20.2", "better-sqlite3": "^9.1.1", "dayjs": "^1.11.10", "googleapis": "^128.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "sharp": "^0.32.6"}}