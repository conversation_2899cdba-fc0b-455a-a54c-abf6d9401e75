"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const common_1 = require("./types/common");
// Define the API that will be exposed to the renderer process
const electronAPI = {
  // Database operations
  database: {
    init: () => electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.DB_INIT),
    query: (sql, params) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.DB_QUERY,
        sql,
        params
      ),
    execute: (sql, params) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.DB_EXECUTE,
        sql,
        params
      ),
  },
  // Building operations
  buildings: {
    create: (data) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.BUILDING_CREATE,
        data
      ),
    update: (data) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.BUILDING_UPDATE,
        data
      ),
    delete: (id) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.BUILDING_DELETE, id),
    getAll: () =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.BUILDING_GET_ALL),
    getById: (id) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.BUILDING_GET_BY_ID,
        id
      ),
    getWithStats: () =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.BUILDING_GET_WITH_STATS
      ),
  },
  // Floor operations
  floors: {
    create: (data) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.FLOOR_CREATE, data),
    update: (data) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.FLOOR_UPDATE, data),
    delete: (id) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.FLOOR_DELETE, id),
    getByBuilding: (buildingId) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.FLOOR_GET_BY_BUILDING,
        buildingId
      ),
    autoGenerate: (buildingId, numberOfFloors) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.FLOOR_AUTO_GENERATE,
        buildingId,
        numberOfFloors
      ),
  },
  // Apartment operations
  apartments: {
    create: (data) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.APARTMENT_CREATE,
        data
      ),
    update: (data) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.APARTMENT_UPDATE,
        data
      ),
    delete: (id) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.APARTMENT_DELETE, id),
    getByFloor: (floorId) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.APARTMENT_GET_BY_FLOOR,
        floorId
      ),
    getWithDetails: (id) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.APARTMENT_GET_WITH_DETAILS,
        id
      ),
    book: (data) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.APARTMENT_BOOK, data),
    checkout: (data) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.APARTMENT_CHECKOUT,
        data
      ),
    getHistory: (apartmentId) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.APARTMENT_GET_HISTORY,
        apartmentId
      ),
  },
  // Customer operations
  customers: {
    create: (data) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.CUSTOMER_CREATE,
        data
      ),
    update: (data) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.CUSTOMER_UPDATE,
        data
      ),
    delete: (id) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.CUSTOMER_DELETE, id),
    getAll: () =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.CUSTOMER_GET_ALL),
    getById: (id) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.CUSTOMER_GET_BY_ID,
        id
      ),
    getAvailable: () =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.CUSTOMER_GET_AVAILABLE
      ),
  },
  // File operations
  files: {
    upload: (fileData, fileName, entityType, entityId, fieldName) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.FILE_UPLOAD,
        fileData,
        fileName,
        entityType,
        entityId,
        fieldName
      ),
    delete: (filePath) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.FILE_DELETE,
        filePath
      ),
    getMetadata: (entityType, entityId) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.FILE_GET_METADATA,
        entityType,
        entityId
      ),
    generateThumbnail: (filePath) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.FILE_GENERATE_THUMBNAIL,
        filePath
      ),
  },
  // Google Drive operations
  googleDrive: {
    authenticate: () =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.GOOGLE_AUTH),
    sync: () =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.GOOGLE_SYNC),
    upload: (data) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.GOOGLE_UPLOAD, data),
    download: (fileId) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.GOOGLE_DOWNLOAD,
        fileId
      ),
  },
  // User operations
  users: {
    create: (data) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.USER_CREATE, data),
    update: (data) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.USER_UPDATE, data),
    getCurrent: () =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.USER_GET_CURRENT),
  },
  // Search and reporting
  search: {
    global: (query, filters) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.SEARCH_GLOBAL,
        query,
        filters
      ),
  },
  reports: {
    generate: (type, options) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.REPORT_GENERATE,
        type,
        options
      ),
  },
  // Data export/import
  dataExport: {
    export: (options) =>
      electron_1.ipcRenderer.invoke(common_1.IPC_CHANNELS.EXPORT_DATA, options),
    import: (filePath) =>
      electron_1.ipcRenderer.invoke(
        common_1.IPC_CHANNELS.IMPORT_DATA,
        filePath
      ),
  },
  // Dialog operations
  dialogs: {
    openFile: (options) =>
      electron_1.ipcRenderer.invoke("dialog:openFile", options),
    saveFile: (options) =>
      electron_1.ipcRenderer.invoke("dialog:saveFile", options),
    showMessage: (options) =>
      electron_1.ipcRenderer.invoke("dialog:showMessage", options),
  },
  // Utility functions
  utils: {
    getAppVersion: () => electron_1.ipcRenderer.invoke("app:getVersion"),
    getAppPath: () => electron_1.ipcRenderer.invoke("app:getPath"),
    openExternal: (url) =>
      electron_1.ipcRenderer.invoke("shell:openExternal", url),
  },
};
// Expose the API to the renderer process
electron_1.contextBridge.exposeInMainWorld("electronAPI", electronAPI);
