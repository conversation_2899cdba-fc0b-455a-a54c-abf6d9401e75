import { app, BrowserWindow, ipc<PERSON>ain, dialog, shell } from "electron";
import { join } from "path";
import { existsSync, mkdirSync } from "fs";
import Database from "better-sqlite3";
import { DatabaseService } from "./services/DatabaseService";
import { FileService } from "./services/FileService";
import { GoogleDriveService } from "./services/GoogleDriveService";
import { IPC_CHANNELS } from "../types/common";

class ElectronApp {
  private mainWindow: BrowserWindow | null = null;
  private databaseService: DatabaseService | null = null;
  private fileService: FileService | null = null;
  private googleDriveService: GoogleDriveService | null = null;

  constructor() {
    this.initializeApp();
  }

  private async initializeApp(): Promise<void> {
    // Handle app events
    app.whenReady().then(() => {
      this.createWindow();
      this.initializeServices();
      this.setupIpcHandlers();
    });

    app.on("window-all-closed", () => {
      if (process.platform !== "darwin") {
        app.quit();
      }
    });

    app.on("activate", () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });
  }

  private createWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 800,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, "preload.js"),
      },
      icon: join(__dirname, "../../public/icon.ico"),
      show: false, // Don't show until ready
      titleBarStyle: "default",
    });

    // Load the app
    if (process.env.NODE_ENV === "development") {
      this.mainWindow.loadURL("http://localhost:3000");
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(join(__dirname, "../renderer/index.html"));
    }

    // Show window when ready
    this.mainWindow.once("ready-to-show", () => {
      this.mainWindow?.show();
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: "deny" };
    });
  }

  private async initializeServices(): Promise<void> {
    try {
      // Ensure data directories exist
      const userDataPath = app.getPath("userData");
      const dbPath = join(userDataPath, "database");
      const filesPath = join(userDataPath, "files");

      if (!existsSync(dbPath)) mkdirSync(dbPath, { recursive: true });
      if (!existsSync(filesPath)) mkdirSync(filesPath, { recursive: true });

      // Initialize database
      const dbFile = join(dbPath, "apartment_rental.db");
      const database = new Database(dbFile);
      this.databaseService = new DatabaseService(database);
      await this.databaseService.initialize();

      // Initialize file service
      this.fileService = new FileService(filesPath);

      // Initialize Google Drive service
      this.googleDriveService = new GoogleDriveService();

      console.log("All services initialized successfully");
    } catch (error) {
      console.error("Failed to initialize services:", error);
      dialog.showErrorBox(
        "Initialization Error",
        "Failed to initialize application services. Please restart the application."
      );
    }
  }

  private setupIpcHandlers(): void {
    // Database operations
    ipcMain.handle(IPC_CHANNELS.DB_INIT, async () => {
      return this.databaseService?.initialize();
    });

    ipcMain.handle(
      IPC_CHANNELS.DB_QUERY,
      async (_, sql: string, params?: any[]) => {
        return this.databaseService?.query(sql, params);
      }
    );

    ipcMain.handle(
      IPC_CHANNELS.DB_EXECUTE,
      async (_, sql: string, params?: any[]) => {
        return this.databaseService?.execute(sql, params);
      }
    );

    // File operations
    ipcMain.handle(
      IPC_CHANNELS.FILE_UPLOAD,
      async (
        _,
        fileData: Buffer,
        fileName: string,
        entityType: string,
        entityId: number,
        fieldName: string
      ) => {
        return this.fileService?.uploadFile(
          fileData,
          fileName,
          entityType,
          entityId,
          fieldName
        );
      }
    );

    ipcMain.handle(IPC_CHANNELS.FILE_DELETE, async (_, filePath: string) => {
      return this.fileService?.deleteFile(filePath);
    });

    ipcMain.handle(
      IPC_CHANNELS.FILE_GENERATE_THUMBNAIL,
      async (_, filePath: string) => {
        return this.fileService?.generateThumbnail(filePath);
      }
    );

    // Google Drive operations
    ipcMain.handle(IPC_CHANNELS.GOOGLE_AUTH, async () => {
      return this.googleDriveService?.authenticate();
    });

    ipcMain.handle(IPC_CHANNELS.GOOGLE_SYNC, async () => {
      return this.googleDriveService?.syncData();
    });

    // Dialog operations
    ipcMain.handle("dialog:openFile", async (_, options) => {
      const result = await dialog.showOpenDialog(this.mainWindow!, options);
      return result;
    });

    ipcMain.handle("dialog:saveFile", async (_, options) => {
      const result = await dialog.showSaveDialog(this.mainWindow!, options);
      return result;
    });

    ipcMain.handle("dialog:showMessage", async (_, options) => {
      const result = await dialog.showMessageBox(this.mainWindow!, options);
      return result;
    });
  }
}

// Create the application instance
new ElectronApp();
