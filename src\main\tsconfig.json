{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "../../dist/main", "noEmit": false, "jsx": "preserve", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true}, "include": ["./**/*", "../types/**/*"], "exclude": ["node_modules"]}