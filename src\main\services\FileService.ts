import {
  writeFileSync,
  unlinkSync,
  existsSync,
  mkdirSync,
  readFileSync,
} from "fs";
import { join, extname, basename } from "path";
import { randomUUID } from "crypto";
import sharp from "sharp";

export class FileService {
  private basePath: string;
  private thumbnailPath: string;

  constructor(basePath: string) {
    this.basePath = basePath;
    this.thumbnailPath = join(basePath, "thumbnails");

    // Ensure directories exist
    if (!existsSync(this.basePath)) {
      mkdirSync(this.basePath, { recursive: true });
    }
    if (!existsSync(this.thumbnailPath)) {
      mkdirSync(this.thumbnailPath, { recursive: true });
    }
  }

  async uploadFile(
    fileData: Buffer,
    originalFileName: string,
    entityType: string,
    entityId: number,
    fieldName: string
  ): Promise<{ filePath: string; thumbnailPath?: string }> {
    try {
      // Generate unique filename
      const fileExtension = extname(originalFileName);
      const uniqueFileName = `${entityType}_${entityId}_${fieldName}_${randomUUID()}${fileExtension}`;
      const filePath = join(this.basePath, uniqueFileName);

      // Save the file
      writeFileSync(filePath, fileData);

      let thumbnailPath: string | undefined;

      // Generate thumbnail for images
      if (this.isImageFile(originalFileName)) {
        const generatedThumbnail = await this.generateThumbnail(filePath);
        thumbnailPath = generatedThumbnail || undefined;
      }

      return {
        filePath: uniqueFileName, // Return relative path
        thumbnailPath: thumbnailPath ? basename(thumbnailPath) : undefined,
      };
    } catch (error) {
      console.error("File upload error:", error);
      throw error;
    }
  }

  deleteFile(fileName: string): boolean {
    try {
      const filePath = join(this.basePath, fileName);
      if (existsSync(filePath)) {
        unlinkSync(filePath);

        // Also delete thumbnail if exists
        const thumbnailFile = this.getThumbnailPath(fileName);
        if (existsSync(thumbnailFile)) {
          unlinkSync(thumbnailFile);
        }

        return true;
      }
      return false;
    } catch (error) {
      console.error("File deletion error:", error);
      return false;
    }
  }

  async generateThumbnail(filePath: string): Promise<string | null> {
    try {
      if (!this.isImageFile(filePath)) {
        return null;
      }

      const fileName = basename(filePath);
      const thumbnailFileName = `thumb_${fileName}`;
      const thumbnailFilePath = join(this.thumbnailPath, thumbnailFileName);

      await sharp(filePath)
        .resize(200, 200, {
          fit: "inside",
          withoutEnlargement: true,
        })
        .jpeg({ quality: 80 })
        .toFile(thumbnailFilePath);

      return thumbnailFilePath;
    } catch (error) {
      console.error("Thumbnail generation error:", error);
      return null;
    }
  }

  getFilePath(fileName: string): string {
    return join(this.basePath, fileName);
  }

  getThumbnailPath(fileName: string): string {
    const thumbnailFileName = `thumb_${fileName}`;
    return join(this.thumbnailPath, thumbnailFileName);
  }

  fileExists(fileName: string): boolean {
    return existsSync(join(this.basePath, fileName));
  }

  getFileBuffer(fileName: string): Buffer | null {
    try {
      const filePath = join(this.basePath, fileName);
      if (existsSync(filePath)) {
        return readFileSync(filePath);
      }
      return null;
    } catch (error) {
      console.error("Error reading file:", error);
      return null;
    }
  }

  private isImageFile(fileName: string): boolean {
    const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
    const extension = extname(fileName).toLowerCase();
    return imageExtensions.includes(extension);
  }

  private isPdfFile(fileName: string): boolean {
    return extname(fileName).toLowerCase() === ".pdf";
  }

  getFileInfo(fileName: string): { size: number; mimeType: string } | null {
    try {
      const filePath = join(this.basePath, fileName);
      if (!existsSync(filePath)) {
        return null;
      }

      const stats = require("fs").statSync(filePath);
      const extension = extname(fileName).toLowerCase();

      let mimeType = "application/octet-stream";

      // Determine MIME type based on extension
      const mimeTypes: Record<string, string> = {
        ".jpg": "image/jpeg",
        ".jpeg": "image/jpeg",
        ".png": "image/png",
        ".gif": "image/gif",
        ".bmp": "image/bmp",
        ".webp": "image/webp",
        ".pdf": "application/pdf",
        ".doc": "application/msword",
        ".docx":
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ".txt": "text/plain",
      };

      mimeType = mimeTypes[extension] || mimeType;

      return {
        size: stats.size,
        mimeType,
      };
    } catch (error) {
      console.error("Error getting file info:", error);
      return null;
    }
  }
}
