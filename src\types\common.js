"use strict";
// Common types and interfaces used across the application
Object.defineProperty(exports, "__esModule", { value: true });
exports.IPC_CHANNELS = void 0;
// IPC Channel names for Electron communication
exports.IPC_CHANNELS = {
    // Database operations
    DB_INIT: 'db:init',
    DB_QUERY: 'db:query',
    DB_EXECUTE: 'db:execute',
    // Building operations
    BUILDING_CREATE: 'building:create',
    BUILDING_UPDATE: 'building:update',
    BUILDING_DELETE: 'building:delete',
    BUILDING_GET_ALL: 'building:getAll',
    BUILDING_GET_BY_ID: 'building:getById',
    BUILDING_GET_WITH_STATS: 'building:getWithStats',
    // Floor operations
    FLOOR_CREATE: 'floor:create',
    FLOOR_UPDATE: 'floor:update',
    FLOOR_DELETE: 'floor:delete',
    FLOOR_GET_BY_BUILDING: 'floor:getByBuilding',
    FLOOR_AUTO_GENERATE: 'floor:autoGenerate',
    // Apartment operations
    APARTMENT_CREATE: 'apartment:create',
    APARTMENT_UPDATE: 'apartment:update',
    APARTMENT_DELETE: 'apartment:delete',
    APARTMENT_GET_BY_FLOOR: 'apartment:getByFloor',
    APARTMENT_GET_WITH_DETAILS: 'apartment:getWithDetails',
    APARTMENT_BOOK: 'apartment:book',
    APARTMENT_CHECKOUT: 'apartment:checkout',
    APARTMENT_GET_HISTORY: 'apartment:getHistory',
    // Customer operations
    CUSTOMER_CREATE: 'customer:create',
    CUSTOMER_UPDATE: 'customer:update',
    CUSTOMER_DELETE: 'customer:delete',
    CUSTOMER_GET_ALL: 'customer:getAll',
    CUSTOMER_GET_BY_ID: 'customer:getById',
    CUSTOMER_GET_AVAILABLE: 'customer:getAvailable',
    // File operations
    FILE_UPLOAD: 'file:upload',
    FILE_DELETE: 'file:delete',
    FILE_GET_METADATA: 'file:getMetadata',
    FILE_GENERATE_THUMBNAIL: 'file:generateThumbnail',
    // Google Drive operations
    GOOGLE_AUTH: 'google:auth',
    GOOGLE_SYNC: 'google:sync',
    GOOGLE_UPLOAD: 'google:upload',
    GOOGLE_DOWNLOAD: 'google:download',
    // User operations
    USER_CREATE: 'user:create',
    USER_UPDATE: 'user:update',
    USER_GET_CURRENT: 'user:getCurrent',
    // Search and reporting
    SEARCH_GLOBAL: 'search:global',
    REPORT_GENERATE: 'report:generate',
    EXPORT_DATA: 'export:data',
    IMPORT_DATA: 'import:data',
};
