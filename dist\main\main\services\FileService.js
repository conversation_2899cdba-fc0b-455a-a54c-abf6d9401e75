"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileService = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const crypto_1 = require("crypto");
const sharp_1 = __importDefault(require("sharp"));
class FileService {
    constructor(basePath) {
        this.basePath = basePath;
        this.thumbnailPath = (0, path_1.join)(basePath, "thumbnails");
        // Ensure directories exist
        if (!(0, fs_1.existsSync)(this.basePath)) {
            (0, fs_1.mkdirSync)(this.basePath, { recursive: true });
        }
        if (!(0, fs_1.existsSync)(this.thumbnailPath)) {
            (0, fs_1.mkdirSync)(this.thumbnailPath, { recursive: true });
        }
    }
    async uploadFile(fileData, originalFileName, entityType, entityId, fieldName) {
        try {
            // Generate unique filename
            const fileExtension = (0, path_1.extname)(originalFileName);
            const uniqueFileName = `${entityType}_${entityId}_${fieldName}_${(0, crypto_1.randomUUID)()}${fileExtension}`;
            const filePath = (0, path_1.join)(this.basePath, uniqueFileName);
            // Save the file
            (0, fs_1.writeFileSync)(filePath, fileData);
            let thumbnailPath;
            // Generate thumbnail for images
            if (this.isImageFile(originalFileName)) {
                const generatedThumbnail = await this.generateThumbnail(filePath);
                thumbnailPath = generatedThumbnail || undefined;
            }
            return {
                filePath: uniqueFileName, // Return relative path
                thumbnailPath: thumbnailPath ? (0, path_1.basename)(thumbnailPath) : undefined,
            };
        }
        catch (error) {
            console.error("File upload error:", error);
            throw error;
        }
    }
    deleteFile(fileName) {
        try {
            const filePath = (0, path_1.join)(this.basePath, fileName);
            if ((0, fs_1.existsSync)(filePath)) {
                (0, fs_1.unlinkSync)(filePath);
                // Also delete thumbnail if exists
                const thumbnailFile = this.getThumbnailPath(fileName);
                if ((0, fs_1.existsSync)(thumbnailFile)) {
                    (0, fs_1.unlinkSync)(thumbnailFile);
                }
                return true;
            }
            return false;
        }
        catch (error) {
            console.error("File deletion error:", error);
            return false;
        }
    }
    async generateThumbnail(filePath) {
        try {
            if (!this.isImageFile(filePath)) {
                return null;
            }
            const fileName = (0, path_1.basename)(filePath);
            const thumbnailFileName = `thumb_${fileName}`;
            const thumbnailFilePath = (0, path_1.join)(this.thumbnailPath, thumbnailFileName);
            await (0, sharp_1.default)(filePath)
                .resize(200, 200, {
                fit: "inside",
                withoutEnlargement: true,
            })
                .jpeg({ quality: 80 })
                .toFile(thumbnailFilePath);
            return thumbnailFilePath;
        }
        catch (error) {
            console.error("Thumbnail generation error:", error);
            return null;
        }
    }
    getFilePath(fileName) {
        return (0, path_1.join)(this.basePath, fileName);
    }
    getThumbnailPath(fileName) {
        const thumbnailFileName = `thumb_${fileName}`;
        return (0, path_1.join)(this.thumbnailPath, thumbnailFileName);
    }
    fileExists(fileName) {
        return (0, fs_1.existsSync)((0, path_1.join)(this.basePath, fileName));
    }
    getFileBuffer(fileName) {
        try {
            const filePath = (0, path_1.join)(this.basePath, fileName);
            if ((0, fs_1.existsSync)(filePath)) {
                return (0, fs_1.readFileSync)(filePath);
            }
            return null;
        }
        catch (error) {
            console.error("Error reading file:", error);
            return null;
        }
    }
    isImageFile(fileName) {
        const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
        const extension = (0, path_1.extname)(fileName).toLowerCase();
        return imageExtensions.includes(extension);
    }
    isPdfFile(fileName) {
        return (0, path_1.extname)(fileName).toLowerCase() === ".pdf";
    }
    getFileInfo(fileName) {
        try {
            const filePath = (0, path_1.join)(this.basePath, fileName);
            if (!(0, fs_1.existsSync)(filePath)) {
                return null;
            }
            const stats = require("fs").statSync(filePath);
            const extension = (0, path_1.extname)(fileName).toLowerCase();
            let mimeType = "application/octet-stream";
            // Determine MIME type based on extension
            const mimeTypes = {
                ".jpg": "image/jpeg",
                ".jpeg": "image/jpeg",
                ".png": "image/png",
                ".gif": "image/gif",
                ".bmp": "image/bmp",
                ".webp": "image/webp",
                ".pdf": "application/pdf",
                ".doc": "application/msword",
                ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".txt": "text/plain",
            };
            mimeType = mimeTypes[extension] || mimeType;
            return {
                size: stats.size,
                mimeType,
            };
        }
        catch (error) {
            console.error("Error getting file info:", error);
            return null;
        }
    }
}
exports.FileService = FileService;
