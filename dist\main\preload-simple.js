const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Simple API for testing
const electronAPI = {
  test: () => console.log('Electron API is working!'),
  database: {
    init: () => Promise.resolve(true),
    query: () => Promise.resolve([]),
    execute: () => Promise.resolve({ changes: 0 }),
  },
  // Add other APIs as needed
};

contextBridge.exposeInMainWorld('electronAPI', electronAPI);
